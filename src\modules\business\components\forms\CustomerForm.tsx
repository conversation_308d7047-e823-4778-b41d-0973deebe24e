import React from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { AxiosError } from 'axios';
import { Form, FormItem, Input, Button, Typography, Card } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import TagsInput from '@/shared/components/common/TagsInput/TagsInput';
import { useFormErrors } from '@/shared/hooks/form';
import { createCustomerFormSchema, CustomerFormValues } from '../../schemas/customer.schema';
import { useCreateConvertCustomer } from '../../hooks/useCustomerQuery';

interface CustomerFormProps {
  /**
   * Dữ liệu ban đầu cho form (dùng cho chỉnh sửa)
   */
  initialData?: Partial<CustomerFormValues>;

  /**
   * Callback khi submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi hủy form
   */
  onCancel: () => void;

  /**
   * Tiêu đề form
   */
  title?: string;

  /**
   * Form ref từ parent component để xử lý lỗi
   */
  formRef?: React.RefObject<FormRef<FieldValues>>;
}

/**
 * Component form thêm/chỉnh sửa khách hàng
 */
const CustomerForm: React.FC<CustomerFormProps> = ({
  initialData,
  onSuccess,
  onCancel,
  title,
  formRef: externalFormRef,
}) => {
  const { t } = useTranslation(['business', 'validation', 'common']);
  // Hook để xử lý lỗi form
  const { formRef: formRefFromHook, setFormErrors } = useFormErrors<CustomerFormValues>();

  // Sử dụng external formRef nếu có, nếu không thì dùng formRef từ hook
  const formRef = (externalFormRef || formRefFromHook) as React.RefObject<FormRef<FieldValues>>;

  // Hook để tạo khách hàng mới
  const createCustomerMutation = useCreateConvertCustomer({
    showSuccessNotification: true,
    showErrorNotification: false, // Để component xử lý lỗi cụ thể
  });

  // Tạo schema với translation
  const customerFormSchema = createCustomerFormSchema(t);

  // Giá trị mặc định cho form
  const defaultValues: CustomerFormValues = {
    name: '',
    email: '',
    phone: '',
    tags: '',
    ...initialData,
  };

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    const formValues = values as CustomerFormValues;

    // Chuyển đổi dữ liệu form sang format API
    const customerData = {
      name: formValues.name,
      phone: formValues.phone,
      email: {
        primary: formValues.email,
      },
      tags: formValues.tags
        ? formValues.tags
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag)
        : [],
    };

    // Sử dụng mutate để có thể handle error trong onError callback
    createCustomerMutation.mutate(customerData, {
      onSuccess: () => {
        // Gọi callback success từ parent
        onSuccess?.();
      },
      onError: (error: AxiosError) => {
        console.error('Error creating customer:', error);

        // Xử lý lỗi API cụ thể
        if (error.response?.data) {
          const errorData = error.response.data as {
            code?: number;
            message?: string;
            errors?: Record<string, string>;
          };

          // Xử lý lỗi số điện thoại đã tồn tại (code 30187)
          if (errorData.code === 30187) {
            setFormErrors({
              phone: errorData.message || 'Số điện thoại đã tồn tại trong hệ thống'
            });
            return;
          }

          // Xử lý lỗi validation từ API (nếu có)
          if (errorData.errors) {
            setFormErrors(errorData.errors);
            return;
          }

          // Xử lý các lỗi khác
          if (errorData.message) {
            setFormErrors({
              general: errorData.message
            });
          }
        }
      },
    });
  };

  return (
    <Card className="space-y-6">
      {/* Header */}
      <div className="border-b border-border pb-4 mb-6">
        <Typography variant="h4" className="text-foreground">
          {title || t('customer.addForm')}
        </Typography>
      </div>

      {/* Form */}
      <Form
        ref={formRef}
        schema={customerFormSchema}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Họ và tên */}
          <FormItem name="name" label={t('customer.form.name')} required>
            <Input
              placeholder={t('customer.form.namePlaceholder')}
              fullWidth
              disabled={createCustomerMutation.isPending}
            />
          </FormItem>

          {/* Email */}
          <FormItem name="email" label={t('customer.form.email')} required>
            <Input
              type="email"
              placeholder={t('customer.form.emailPlaceholder')}
              fullWidth
              disabled={createCustomerMutation.isPending}
            />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Số điện thoại */}
          <FormItem name="phone" label={t('customer.form.phone')} required>
            <Input
              type="tel"
              placeholder={t('customer.form.phonePlaceholder')}
              fullWidth
              disabled={createCustomerMutation.isPending}
            />
          </FormItem>

          {/* Tag khách hàng */}
          <FormItem name="tags" label={t('customer.form.tags')}>
            <TagsInput
              fieldName="tags"
              placeholder={t('customer.form.tagsPlaceholder')}
              formRef={formRef}
              initialValue={initialData?.tags}
              readOnly={createCustomerMutation.isPending}
            />
          </FormItem>
        </div>

        {/* Action buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={createCustomerMutation.isPending}
            className="min-w-[100px]"
          >
            {t('common.cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={createCustomerMutation.isPending}
            className="min-w-[100px]"
          >
            {t('common.save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CustomerForm;
export type { CustomerFormValues };