import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  IconCard,
  Tooltip,
  FormItem,
  Select,
  Textarea,
  Input,
  Chip,
  Divider,
  Icon,
} from '@/shared/components/common';
import CustomerSelectorWithDropdown from '../order/CustomerSelectorWithDropdown';
import ProductSelector from '../order/ProductSelector';
import OrderSummary from '../order/OrderSummary';
import {
  OrderCustomerDto,
  OrderItemDto,
  ShippingDto,
  ShippingMethod,
  CreateEnhancedOrderDto,
} from '../../types/order.types';
import { PaymentMethod } from '../../services/order.service';

interface EnhancedOrderFormProps {
  onSubmit: (orderData: CreateEnhancedOrderDto) => Promise<void>;
  onCancel: () => void;
  isSubmitting: boolean;
}

/**
 * Form tạo đơn hàng mới với tích hợp vận chuyển
 */
const EnhancedOrderForm: React.FC<EnhancedOrderFormProps> = ({
  onSubmit,
  onCancel,
  isSubmitting,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // State cho form
  const [selectedCustomer, setSelectedCustomer] = useState<OrderCustomerDto>();
  const [selectedItems, setSelectedItems] = useState<OrderItemDto[]>([]);
  const [shipping, setShipping] = useState<ShippingDto>();
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.CASH);
  const [paymentStatus, setPaymentStatus] = useState<string>('UNPAID');
  const [codAmount, setCodAmount] = useState<number>(0);
  const [notes, setNotes] = useState<string>('');
  const [tags, setTags] = useState<string[]>([]);
  const [tempTag, setTempTag] = useState<string>('');

  // State cho UI
  const [currentStep, setCurrentStep] = useState<number>(1);

  // Xử lý thay đổi thông tin khách hàng
  const handleCustomerChange = useCallback((customerData: Partial<OrderCustomerDto>) => {
    setSelectedCustomer(prev => ({
      ...prev,
      ...customerData,
    } as OrderCustomerDto));
  }, []);

  // Xử lý thêm tag
  const handleAddTag = useCallback((tag: string) => {
    if (tag.trim() && !tags.includes(tag.trim())) {
      setTags(prev => [...prev, tag.trim()]);
      setTempTag('');
    }
  }, [tags]);

  // Xử lý xóa tag
  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  }, []);

  // Xử lý submit form
  const handleSubmit = useCallback(async () => {
    if (!selectedCustomer?.id || selectedItems.length === 0) {
      return;
    }

    const orderData: CreateEnhancedOrderDto = {
      customer: selectedCustomer,
      items: selectedItems,
      shipping: shipping || {
        method: ShippingMethod.SELF,
        fromAddress: {
          province: '',
          district: '',
          ward: '',
          address: '',
        },
        toAddress: selectedCustomer.address,
        fee: 0,
      },
      payment: {
        method: paymentMethod,
        status: paymentStatus,
        codAmount: paymentMethod === PaymentMethod.CASH ? codAmount : undefined,
      },
      notes: notes || undefined,
      tags: tags.length > 0 ? tags : undefined,
    };

    await onSubmit(orderData);
  }, [selectedCustomer, selectedItems, shipping, paymentMethod, paymentStatus, codAmount, notes, tags, onSubmit]);

  // Kiểm tra có thể chuyển bước tiếp theo
  const canProceedToNextStep = useCallback(() => {
    switch (currentStep) {
      case 1:
        // Yêu cầu đã chọn khách hàng và có ít nhất tên và một trong số phone hoặc email
        return selectedCustomer?.id && selectedCustomer?.name &&
               (selectedCustomer?.phone || selectedCustomer?.email);
      case 2:
        return selectedItems.length > 0;
      case 3:
        return true; // Shipping là optional
      case 4:
        return paymentMethod;
      default:
        return false;
    }
  }, [currentStep, selectedCustomer, selectedItems, paymentMethod]);

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      { number: 1, title: t('business:order.steps.customer') },
      { number: 2, title: t('business:order.steps.products') },
      { number: 3, title: t('business:order.steps.shipping') },
      { number: 4, title: t('business:order.steps.payment') },
      { number: 5, title: t('business:order.steps.review') },
    ];

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => (
          <React.Fragment key={step.number}>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                currentStep >= step.number
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {currentStep > step.number ? (
                <Icon name="check" size="sm" />
              ) : (
                step.number
              )}
            </div>
            <div className="ml-2 mr-4">
              <Typography
                variant="caption"
                className={currentStep >= step.number ? 'text-primary font-medium' : 'text-gray-500'}
              >
                {step.title}
              </Typography>
            </div>
            {index < steps.length - 1 && (
              <div
                className={`w-8 h-0.5 mx-2 ${
                  currentStep > step.number ? 'bg-primary' : 'bg-gray-200'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <CustomerSelectorWithDropdown
            selectedCustomer={selectedCustomer}
            onCustomerSelect={setSelectedCustomer}
            onCustomerChange={handleCustomerChange}
            placeholder={t('business:order.searchCustomerPlaceholder', 'Tìm kiếm khách hàng...')}
          />
        );

      case 2:
        return (
          <ProductSelector
            selectedItems={selectedItems}
            onItemsChange={setSelectedItems}
          />
        );

      case 3:
        return (
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('business:order.shippingInfo')}
              </Typography>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem label={t('business:order.shippingMethod')}>
                  <Select
                    value={shipping?.method || ShippingMethod.SELF}
                    onChange={(value) => setShipping(prev => ({
                      ...prev,
                      method: value as ShippingMethod,
                    } as ShippingDto))}
                    options={[
                      { value: ShippingMethod.SELF, label: t('business:order.shipping.self') },
                      { value: ShippingMethod.GHN, label: t('business:order.shipping.ghn') },
                      { value: ShippingMethod.GHTK, label: t('business:order.shipping.ghtk') },
                    ]}
                    fullWidth
                  />
                </FormItem>
                <FormItem label={t('business:order.shippingFee')}>
                  <Input
                    type="number"
                    value={shipping?.fee || 0}
                    onChange={(e) => setShipping(prev => ({
                      ...prev,
                      fee: parseFloat(e.target.value) || 0,
                    } as ShippingDto))}
                    placeholder="0"
                    fullWidth
                  />
                </FormItem>
              </div>
              <FormItem label={t('business:order.shippingNote')}>
                <Textarea
                  value={shipping?.note || ''}
                  onChange={(e) => setShipping(prev => ({
                    ...prev,
                    note: e.target.value,
                  } as ShippingDto))}
                  placeholder={t('business:order.shippingNotePlaceholder')}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>
          </Card>
        );

      case 4:
        return (
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('business:order.paymentInfo')}
              </Typography>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem label={t('business:order.paymentMethod')} required>
                  <Select
                    value={paymentMethod}
                    onChange={(value) => setPaymentMethod(value as PaymentMethod)}
                    options={[
                      { value: PaymentMethod.CASH, label: t('business:order.paymentMethods.cash') },
                      { value: PaymentMethod.CREDIT_CARD, label: t('business:order.paymentMethods.creditCard') },
                      { value: PaymentMethod.BANK_TRANSFER, label: t('business:order.paymentMethods.bankTransfer') },
                      { value: PaymentMethod.DIGITAL_WALLET, label: t('business:order.paymentMethods.digitalWallet') },
                    ]}
                    fullWidth
                  />
                </FormItem>
                <FormItem label={t('business:order.paymentStatus.title')} required>
                  <Select
                    value={paymentStatus}
                    onChange={(value) => setPaymentStatus(value as string)}
                    options={[
                      { value: 'UNPAID', label: t('business:order.paymentStatus.unpaid') },
                      { value: 'PAID', label: t('business:order.paymentStatus.paid') },
                      { value: 'PARTIALLY_PAID', label: t('business:order.paymentStatus.partiallyPaid') },
                    ]}
                    fullWidth
                  />
                </FormItem>
              </div>
              {paymentMethod === PaymentMethod.CASH && (
                <FormItem label={t('business:order.codAmount')}>
                  <Input
                    type="number"
                    value={codAmount}
                    onChange={(e) => setCodAmount(parseFloat(e.target.value) || 0)}
                    placeholder="0"
                    fullWidth
                  />
                </FormItem>
              )}
            </div>
          </Card>
        );

      case 5:
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <Card>
                <div className="p-6">
                  <Typography variant="h6" className="mb-4">
                    {t('business:order.additionalInfo')}
                  </Typography>
                  <FormItem label={t('business:order.notes')}>
                    <Textarea
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder={t('business:order.notesPlaceholder')}
                      rows={4}
                      fullWidth
                    />
                  </FormItem>
                  <FormItem label={t('business:order.tags')}>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input
                          value={tempTag}
                          onChange={(e) => setTempTag(e.target.value)}
                          placeholder={t('business:order.tagsPlaceholder')}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && tempTag.trim()) {
                              e.preventDefault();
                              handleAddTag(tempTag);
                            }
                          }}
                          fullWidth
                        />
                        <Tooltip content={t('common:add')}>
                          <IconCard
                            icon="plus"
                            variant="primary"
                            size="md"
                            onClick={() => handleAddTag(tempTag)}
                            disabled={!tempTag.trim()}
                          />
                        </Tooltip>
                      </div>
                      {tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {tags.map((tag, index) => (
                            <Chip
                              key={index}
                              size="sm"
                              closable
                              onClose={() => handleRemoveTag(tag)}
                            >
                              {tag}
                            </Chip>
                          ))}
                        </div>
                      )}
                    </div>
                  </FormItem>
                </div>
              </Card>
            </div>
            <div>
              <OrderSummary
                customer={selectedCustomer}
                items={selectedItems}
                shipping={shipping}
                paymentMethod={paymentMethod}
                notes={notes}
                tags={tags}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h4" className="mb-6">
          {t('business:order.createOrder')}
        </Typography>

        {renderStepIndicator()}

        <div className="mb-8">
          {renderStepContent()}
        </div>

        <Divider className="my-6" />

        {/* Navigation buttons */}
        <div className="flex justify-between">
          <div>
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={() => setCurrentStep(prev => prev - 1)}
                disabled={isSubmitting}
              >
                <Icon name="chevron-left" size="sm" className="mr-1" />
                {t('common:previous')}
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Tooltip content={t('common:cancel')}>
              <IconCard
                icon="x"
                variant="secondary"
                size="md"
                onClick={onCancel}
                disabled={isSubmitting}
              />
            </Tooltip>

            {currentStep < 5 ? (
              <Button
                onClick={() => setCurrentStep(prev => prev + 1)}
                disabled={!canProceedToNextStep() || isSubmitting}
              >
                {t('common:next')}
                <Icon name="chevron-right" size="sm" className="ml-1" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                isLoading={isSubmitting}
                disabled={!canProceedToNextStep()}
              >
                {t('business:order.createOrder')}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EnhancedOrderForm;
