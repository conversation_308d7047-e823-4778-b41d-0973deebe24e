import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  FormItem,
  Input,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { CustomerService } from '../../services/customer.service';
import { OrderCustomerDto, AddressDto } from '../../types/order.types';
import { UserConvertCustomerListItemDto } from '../../types/customer.types';

interface CustomerSelectorWithDropdownProps {
  selectedCustomer?: OrderCustomerDto;
  onCustomerSelect: (customer: OrderCustomerDto) => void;
  onCustomerChange: (customer: Partial<OrderCustomerDto>) => void;
  placeholder?: string;
  className?: string;
}

/**
 * Component chọn khách hàng với dropdown sử dụng AsyncSelectWithPagination
 * <PERSON><PERSON> chọn sẽ hiển thị form thông tin khách hàng
 */
const CustomerSelectorWithDropdown: React.FC<CustomerSelectorWithDropdownProps> = ({
  selectedCustomer,
  onCustomerSelect,
  onCustomerChange,
  placeholder = 'Tìm kiếm khách hàng...',
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Helper function to safely extract email from customer data
  const getCustomerEmail = (email: { primary?: string } | string | null): string => {
    if (!email) return '';
    if (typeof email === 'string') return email;
    return email.primary || '';
  };

  // Store loaded customers for later use
  const [loadedCustomers, setLoadedCustomers] = useState<Map<string, UserConvertCustomerListItemDto>>(new Map());

  // Load customers function for AsyncSelectWithPagination
  const loadCustomers = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await CustomerService.getConvertCustomers({
        search: params.search || '',
        page: params.page || 1,
        limit: params.limit || 20,
      });

      // Store customers in state for later retrieval
      const newCustomers = new Map(loadedCustomers);
      response.result.items.forEach((customer: UserConvertCustomerListItemDto) => {
        newCustomers.set(customer.id.toString(), customer);
      });
      setLoadedCustomers(newCustomers);

      return {
        items: response.result.items.map((customer: UserConvertCustomerListItemDto) => ({
          value: customer.id.toString(),
          label: customer.name || 'N/A',
          subtitle: `${customer.phone || 'N/A'} • ${getCustomerEmail(customer.email) || 'N/A'}`,
          data: customer as unknown as Record<string, unknown>,
        })),
        totalItems: response.result.meta.totalItems,
        totalPages: response.result.meta.totalPages,
        currentPage: response.result.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading customers:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  }, [loadedCustomers]);

  // Handle customer selection
  const handleCustomerSelect = useCallback((value: string | number | string[] | number[] | undefined) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      onCustomerSelect({} as OrderCustomerDto);
      return;
    }

    // Get the first value if it's an array
    const customerId = Array.isArray(value) ? value[0] : value;
    const customerIdStr = customerId.toString();

    // Get customer data from loaded customers
    const customerData = loadedCustomers.get(customerIdStr);

    if (customerData) {
      // Create order customer with actual data
      const orderCustomer: OrderCustomerDto = {
        id: customerData.id,
        name: customerData.name || '',
        email: getCustomerEmail(customerData.email) || '',
        phone: customerData.phone || '',
        address: {
          province: '',
          district: '',
          ward: '',
          address: '',
        },
      };
      console.log('Selected customer data:', orderCustomer);
      onCustomerSelect(orderCustomer);
    } else {
      // Fallback: create basic customer object if data not found
      const orderCustomer: OrderCustomerDto = {
        id: typeof customerId === 'string' ? parseInt(customerId) : customerId,
        name: '',
        email: '',
        phone: '',
        address: {
          province: '',
          district: '',
          ward: '',
          address: '',
        },
      };
      onCustomerSelect(orderCustomer);
    }
  }, [onCustomerSelect, loadedCustomers, getCustomerEmail]);

  // Xử lý thay đổi thông tin khách hàng
  const handleCustomerInfoChange = useCallback((field: string, value: string) => {
    if (field.startsWith('address.')) {
      const addressField = field.replace('address.', '');
      onCustomerChange({
        address: {
          ...selectedCustomer?.address,
          [addressField]: value,
        } as AddressDto,
      });
    } else {
      onCustomerChange({ [field]: value });
    }
  }, [selectedCustomer, onCustomerChange]);

  // Render thông tin khách hàng đã chọn
  const renderSelectedCustomerDetails = () => {
    if (!selectedCustomer?.id) return null;

    return (
      <Card className="mt-4 bg-card border-border">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6" className="text-card-foreground">
              {t('business:order.customerInfo')}
            </Typography>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onCustomerSelect({} as OrderCustomerDto)}
              className="text-muted-foreground hover:text-foreground"
            >
              <Icon name="x" size="sm" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('business:customer.form.name')}>
              <Input
                value={selectedCustomer?.name || ''}
                onChange={(e) => handleCustomerInfoChange('name', e.target.value)}
                placeholder={t('business:customer.form.namePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.phone')}>
              <Input
                value={selectedCustomer?.phone || ''}
                onChange={(e) => handleCustomerInfoChange('phone', e.target.value)}
                placeholder={t('business:customer.form.phonePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.email')}>
              <Input
                type="email"
                value={selectedCustomer?.email || ''}
                onChange={(e) => handleCustomerInfoChange('email', e.target.value)}
                placeholder={t('business:customer.form.emailPlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.address')}>
              <Input
                value={selectedCustomer?.address?.address || ''}
                onChange={(e) => handleCustomerInfoChange('address.address', e.target.value)}
                placeholder={t('business:customer.form.addressPlaceholder')}
                fullWidth
              />
            </FormItem>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className={className}>
      <Typography variant="h6" className="mb-4">
        {t('business:order.selectCustomer')}
      </Typography>

      {/* AsyncSelectWithPagination cho chọn khách hàng */}
      {!selectedCustomer?.id && (
        <AsyncSelectWithPagination
          loadOptions={loadCustomers}
          placeholder={placeholder}
          searchOnEnter={true}
          debounceTime={300}
          itemsPerPage={20}
          noOptionsMessage={t('business:order.noCustomersFound', 'Không tìm thấy khách hàng')}
          loadingMessage={t('common:loading', 'Đang tải...')}
          onChange={handleCustomerSelect}
          fullWidth
        />
      )}

      {/* Render selected customer details */}
      {renderSelectedCustomerDetails()}
    </div>
  );
};

export default CustomerSelectorWithDropdown;
