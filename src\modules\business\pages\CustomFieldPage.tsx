import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip, ConfirmDeleteModal, Button, Typography, Icon } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useCustomFields, useDeleteCustomField, useDeleteMultipleCustomFields } from '../hooks/useCustomFieldQuery';
import { CustomFieldQueryParams, CustomFieldListItem } from '../services/custom-field.service';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import CustomFieldForm from '../components/forms/CustomFieldForm';
import CustomFieldDetailForm from '../components/forms/CustomFieldDetailForm';

/**
 * Trang quản lý trường tùy chỉnh
 */
const CustomFieldPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // State cho form và modal
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedFieldId, setSelectedFieldId] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (id: number) => {
      setSelectedFieldId(id);
      showForm();
    },
    [setSelectedFieldId, showForm]
  );

  // Xử lý hiển thị modal xác nhận xóa
  const handleShowDeleteConfirm = useCallback(
    (id: number) => {
      setSelectedFieldId(id);
      setIsDeleteModalOpen(true);
    },
    [setSelectedFieldId]
  );

  // Định nghĩa cột cho bảng
  const columns: TableColumn<CustomFieldListItem>[] = useMemo(
    () => [
      {
        key: 'label',
        title: t('business:customField.name'),
        dataIndex: 'label',
      },
      {
        key: 'configId',
        title: t('business:customField.configId'),
        dataIndex: 'configId',
      },
      {
        key: 'type',
        title: t('business:customField.type'),
        dataIndex: 'type',
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions'),
        render: (_, record) => {
          return (
            <div className="flex space-x-2">
              <Tooltip content={t('common:edit')}>
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEdit(record.id)}
                />
              </Tooltip>
            </div>
          );
        },
      },
    ],
    [t, handleEdit, handleShowDeleteConfirm]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): CustomFieldQueryParams => {
    const queryParams: CustomFieldQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.type = params.filterValue as string;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<CustomFieldListItem, CustomFieldQueryParams>({
      columns,
      filterOptions: [
        { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
        {
          id: 'string',
          label: t('business:customField.type.string'),
          icon: 'text',
          value: 'string',
        },
        {
          id: 'number',
          label: t('business:customField.type.number'),
          icon: 'hash',
          value: 'number',
        },
        {
          id: 'boolean',
          label: t('business:customField.type.boolean'),
          icon: 'toggle-left',
          value: 'boolean',
        },
        { id: 'date', label: t('business:customField.type.date'), icon: 'calendar', value: 'date' },
      ],
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách trường tùy chỉnh
  const { data: customFieldsData, isLoading } = useCustomFields(dataTable.queryParams);

  // Mutation để xóa trường tùy chỉnh
  const deleteCustomFieldMutation = useDeleteCustomField();
  const deleteMultipleCustomFieldsMutation = useDeleteMultipleCustomFields();

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback(
    (column: string | null, order: SortOrder | null) => {
      // Nếu column hoặc order là null, reset sort
      if (column === null || order === null) {
        dataTable.tableData.handleSortChange(null, null);
        return;
      }

      dataTable.tableData.handleSortChange(column, order as SortOrder);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      string: t('business:customField.type.string'),
      number: t('business:customField.type.number'),
      boolean: t('business:customField.type.boolean'),
      date: t('business:customField.type.date'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setSelectedFieldId(null);
  };

  // Xử lý xác nhận xóa
  const handleConfirmDelete = () => {
    if (selectedFieldId) {
      deleteCustomFieldMutation.mutate(selectedFieldId, {
        onSuccess: () => {
          setIsDeleteModalOpen(false);
          setSelectedFieldId(null);
        },
      });
    }
  };

  // Xử lý submit form
  const handleSubmit = () => {
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    setSelectedFieldId(null);
    hideForm();
  };

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều trường tùy chỉnh cùng lúc
      await deleteMultipleCustomFieldsMutation.mutateAsync(selectedRowKeys as number[]);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error deleting custom fields:', error);
    }
  }, [selectedRowKeys, deleteMultipleCustomFieldsMutation]);

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Bulk actions bar khi có items được chọn */}
      {selectedRowKeys.length > 0 && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Typography variant='body2'>
                {t('business:customField.selectedItems', 'Đã chọn {{count}} mục', { count: selectedRowKeys.length })}
              </Typography>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedRowKeys([])}
              >
                {t('common:clearSelection', 'Bỏ chọn tất cả')}
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Icon name="trash" size="sm" />}
                onClick={handleShowBulkDeleteConfirm}
              >
                {t('common:delete', 'Xóa')}
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {selectedFieldId ? (
          <CustomFieldDetailForm
            id={selectedFieldId}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        ) : (
          <CustomFieldForm onSubmit={handleSubmit} onCancel={handleCancel} />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('business:customField.confirmDeleteMessage')}
        isSubmitting={deleteCustomFieldMutation.isPending}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t('business:customField.bulkDeleteConfirmMessage', 'Bạn có chắc chắn muốn xóa {{count}} trường tùy chỉnh đã chọn?', { count: selectedRowKeys.length })}
        isSubmitting={deleteMultipleCustomFieldsMutation.isPending}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={customFieldsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: customFieldsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: customFieldsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default CustomFieldPage;
